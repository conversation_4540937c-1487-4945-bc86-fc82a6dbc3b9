import re
import json
from test_lub import LUB
from test_bearing import BEARING_FUZZY
from config.config_env import file_base_url
import uuid


from utils.utils import format_features_to_string
from test_data_wave_analysis import get_dataset_data_graph_npy, process_multiple_waves, stitch_images
from utils.upload import upload_files

# 配置参数化
args = None

def feature(item):
    try: 
        try:
            # 打开文件
            with open(item.wavePath, 'r') as file:
                # 读取文件内容
                data = file.read()
            # 使用正则表达式找到所有浮点数
            float_regex = r"[-+]?\d*\.?\d+(?:[eE][-+]?\d+)?"        # 将列表转换为 NumPy 数组
            message_value = re.findall(float_regex, data)
            # 将科学计数法表示的字符串转换为浮点数，并去掉科学计数法
            float_data = [float('{:.20f}'.format(float(x))) for x in message_value]

        except Exception as error:
            print("数据文件读取异常====== %s", error)

        # 数据预处理（获取23种时频特征）
        datasets = format_features_to_string(float_data, item.samplingFrequency)
        Test = LUB()
        _,_,lub_freq_values,_ = Test.diagnosis(float_data)

        Test = BEARING_FUZZY()
        _,_,bearing_freq_values,_ = Test.diagnosis(float_data)
        
        # 将datasets字符串解析为JSON对象
        datasets_json = json.loads(datasets)
        # 添加新的数据
        datasets_json["润滑指标"] = lub_freq_values
        datasets_json["轴承指标"] = bearing_freq_values
        
        # 包装最终结果
        final_result = {
            "特征说明": "润滑面积: 信号谱图谱图2000Hz以上频率成分的底带噪声能量值。轴承：中心故障频率：信号谱图中轴承故障的基频故障频率，故障基频幅值：信号谱图中轴承故障的基频故障幅值，故障平均幅值：信号谱图中轴承故障的1到4倍谱线的平均幅值，故障边频：一般为转频或故障调制频率，边频幅值：转频或调制故障的幅值；",
            "提取特征": datasets_json
        }
        
        result = json.dumps(final_result, ensure_ascii=False)
        print("组合后的JSON数据: %s", result)
        return result
        
    except Exception as e:
        print('在线训练异常信息：%s', e)
    
def wave(item):
    
    # 用于存储所有文件中的浮点数数据
    all_float_data = []
    float_regex = r"[-+]?\d*\.?\d+(?:[eE][-+]?\d+)?"

    try:
        # 遍历每个文件路径
        for wave_path in item.wavePaths:
            try:
                wave_path = wave_path
                # 打开文件
                with open(wave_path, 'r') as file:
                    # 读取文件内容
                    data = file.read()
                
                # 提取所有数值字符串
                message_value = re.findall(float_regex, data)
                
                # 转换并格式化浮点数（合并到总数组）
                all_float_data.append(
                    [float('{:.20f}'.format(float(x))) for x in message_value]
                )
                
            except Exception as error:
                print(f"文件 {wave_path} 读取异常: {error}")
                # 可以选择继续处理下一个文件或抛出异常
                # 这里选择继续处理下一个文件
                
    except Exception as global_error:
        print(f"全局异常: {global_error}")

    print("开始执行特征提取", all_float_data)
    # output_dir = "/opt/"  # 指定保存图片的目录
    image_files = process_multiple_waves(all_float_data, item.samplingFrequency, file_base_url)
    print(f"生成的图片路径列表: {image_files}")  # 获取生成的图片路径列表

    # 配置参数
    # USER_ID = "abc-123"
    #
    # # 执行上传
    # uploaded_ids = upload_files(image_files, USER_ID)

    # # 生成随机文件名并拼接图片
    # random_filename = f"{uuid.uuid4()}.png"
    # # 拼接生成的图片，并保存为一张大图
    # output_path = stitch_images(image_files, output_dir, max_cols=4, output_file={random_filename})
    # print(f"生成的图片路径: {output_path}")

    return image_files



